terraform {
  required_version = ">= 1.5.0"
  required_providers {
    auth0 = {
      source  = "auth0/auth0"
      version = ">= 1.0.0"
    }
  }
  # Temporarily using local backend for testing
  # backend "s3" {
  #   bucket         = "qv-terraform"
  #   region         = "ap-southeast-2"
  #   dynamodb_table = "terraform-state-lock"
  #   role_arn = "arn:aws:iam::************:role/ci-cd-account-service-role"
  # }
}
