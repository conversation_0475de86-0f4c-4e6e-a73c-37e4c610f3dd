resource "auth0_tenant" "main" {
  friendly_name = "QV Monarch ${var.env}"

  # Configure session settings
  session_lifetime = 168
  idle_session_lifetime = 72

  # Enable flags that support modern authentication
  flags {
    enable_client_connections = true
    enable_apis_section = true
    enable_pipeline2 = true
    enable_dynamic_client_registration = false
    enable_custom_domain_in_emails = false
    revoke_refresh_token_grant = false
    disable_clickjack_protection_headers = false
    enable_public_signup_user_exists_error = true
    use_scope_descriptions_for_consent = false
    no_disclose_enterprise_connections = false
    disable_management_api_sms_obfuscation = false
    disable_fields_map_fix = false
    enable_adfs_waad_email_verification = false
    enable_sso = true
    allow_legacy_delegation_grant_types = false
    allow_legacy_ro_grant_types = false
    allow_legacy_tokeninfo_endpoint = false
    enable_legacy_profile = false
    enable_idtoken_api2 = false
  }
}
