resource "auth0_client" "monarch_web" {
  allowed_clients = []
  allowed_logout_urls = var.env == "prod" ? ["${var.monarch_url}/authorize"] : ["http://localhost:9000/authorize", "${var.monarch_url}/authorize"]
  allowed_origins = var.env == "prod" ? [var.monarch_url] : ["http://localhost:9000", var.monarch_url]
  app_type        = "regular_web"
  callbacks       = var.env == "prod" ? ["${var.monarch_url}/callback"] : ["http://localhost:9000/callback", "${var.monarch_url}/callback"]

  client_aliases = []
  client_metadata = {}
  cross_origin_auth = true
  description       = ""
  grant_types = [
    "authorization_code",
    "client_credentials",
    "http://auth0.com/oauth/grant-type/mfa-oob",
    "http://auth0.com/oauth/grant-type/mfa-otp",
    "http://auth0.com/oauth/grant-type/mfa-recovery-code",
    "http://auth0.com/oauth/grant-type/password-realm",
    "implicit",
    "password",
    "refresh_token"
  ]
  is_first_party  = true
  logo_uri        = "https://static.wikia.nocookie.net/the-know-your-meme-archive/images/1/16/AwesomeFace-1.png"
  name            = "Monarch Web ${var.env}"
  oidc_conformant = false
  sso             = true
  web_origins = var.env == "prod" ? [var.monarch_url] : ["http://localhost:9000", var.monarch_url]


  jwt_configuration {
    alg                 = "HS256"
    lifetime_in_seconds = 43200
    scopes = {}
    secret_encoded      = false
  }

  refresh_token {
    expiration_type              = "non-expiring"
    idle_token_lifetime          = 1296000
    infinite_idle_token_lifetime = true
    infinite_token_lifetime      = true
    leeway                       = 0
    rotation_type                = "non-rotating"
    token_lifetime               = 2592000
  }
}

resource "auth0_client_credentials" "monarch_web" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.monarch_web.client_id
}

resource "auth0_client_grant" "monarch_web_management_api_grant" {
  audience  = "https://${var.auth0_domain}/api/v2/"
  client_id = auth0_client.monarch_web.client_id
  scopes = [
    "read:users",
    "update:users",
    "delete:users",
    "create:users",
    "read:users_app_metadata",
    "update:users_app_metadata",
    "delete:users_app_metadata",
    "create:users_app_metadata",
    "read:user_custom_blocks",
    "create:user_custom_blocks",
    "delete:user_custom_blocks",
    "create:user_tickets",
    "read:clients",
    "update:clients",
    "delete:clients",
    "create:clients",
    "read:client_keys",
    "update:client_keys",
    "delete:client_keys",
    "create:client_keys",
    "read:connections",
    "update:connections",
    "delete:connections",
    "create:connections",
    "read:resource_servers",
    "update:resource_servers",
    "delete:resource_servers",
    "create:resource_servers",
    "read:device_credentials",
    "update:device_credentials",
    "delete:device_credentials",
    "create:device_credentials",
    "read:rules",
    "update:rules",
    "delete:rules",
    "create:rules",
    "read:mfa_policies",
    "update:mfa_policies",
    "read:guardian_factors",
    "update:guardian_factors",
    "read:tenant_settings",
    "update:tenant_settings"
  ]
}
