variable "env" {
  type        = string
  description = "The env or stack name."
}

variable "auth0_domain" {
  type        = string
  description = "The domain name for the auth0."
}

variable "auth0_client_id" {
  type        = string
  description = "The client id for the auth0 terraform management client."
}

variable "auth0_client_secret" {
  type        = string
  description = "The client secret for the auth0 terraform management client."
}

variable "monarch_url" {
  type        = string
  description = "The url for the monarch web app."
}

variable "db_password" {
  type        = string
  description = "Database password for Auth0 connection"
  sensitive   = true
}

variable "db_user_name" {
  type        = string
  description = "Database username for Auth0 connection"
}

variable "db_server_host" {
  type        = string
  description = "Database server host for Auth0 connection"
}

variable "db_server_port" {
  type        = string
  description = "Database server port for Auth0 connection"
}

variable "external_qivs_url" {
  type        = string
  description = "External QIVS URL for Auth0 connection"
}

variable "enable_webauthn_platform" {
  type        = bool
  description = "Enable WebAuthn platform authenticators (passkeys on same device)"
  default     = true
}

variable "enable_webauthn_roaming" {
  type        = bool
  description = "Enable WebAuthn roaming authenticators (external security keys)"
  default     = true
}